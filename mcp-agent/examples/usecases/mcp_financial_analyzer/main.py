"""
Shortage Analyzer Testing Tool with SSE Transport Integration
------------------------------------------------------------
A focused testing tool for shortage analysis using the shortage_analyzer_agent
with MCP SSE transport to the agent_develop shortage-index server.
Includes server availability validation and startup guidance.
"""

import asyncio
import logging
import aiohttp
import subprocess
import sys
import time
from pathlib import Path
from mcp_agent.app import MCPApp

# Set up logging for shortage analysis testing
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import shortage analyzer agent and alert manager agent
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent
from schemas.agent_schemas import AlertManagementInputSchema, AlertManagementOutputSchema

# Initialize app for integrated workflow testing
app = MCPApp(name="financial_analyzer_workflow", human_input_callback=None)

class DualServerManager:
    """Manage both SSE shortage-index and alert-notification servers for integrated workflow"""
    
    def __init__(self):
        # Shortage-index server configuration
        self.shortage_server_url = "http://localhost:6970"
        self.shortage_server_path = Path("/merge/agent_develop/index/server.py")
        self.shortage_health_endpoint = f"{self.shortage_server_url}/sse"
        
        # Alert-notification server configuration
        self.alert_server_url = "http://localhost:6969"
        self.alert_server_path = Path("/merge/agent_develop/notification/server.py")
        self.alert_health_endpoint = f"{self.alert_server_url}/sse"
        
    async def check_shortage_server_health(self, timeout: int = 5) -> bool:
        """Check if the shortage-index server is running and healthy"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.get(self.shortage_health_endpoint) as response:
                    # For SSE endpoint, any response (even if not 200) indicates server is running
                    logger.info(f"✓ Shortage-index server is running at {self.shortage_server_url}")
                    return True
        except aiohttp.ClientConnectorError:
            logger.warning(f"✗ Cannot connect to shortage-index server at {self.shortage_server_url}")
            return False
        except asyncio.TimeoutError:
            logger.warning(f"✗ Timeout connecting to shortage-index server at {self.shortage_server_url}")
            return False
        except Exception as e:
            logger.warning(f"✗ Shortage server health check failed: {e}")
            return False
    
    async def check_alert_server_health(self, timeout: int = 5) -> bool:
        """Check if the alert-notification server is running and healthy"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.get(self.alert_health_endpoint) as response:
                    # For SSE endpoint, any response (even if not 200) indicates server is running
                    logger.info(f"✓ Alert-notification server is running at {self.alert_server_url}")
                    return True
        except aiohttp.ClientConnectorError:
            logger.warning(f"✗ Cannot connect to alert-notification server at {self.alert_server_url}")
            return False
        except asyncio.TimeoutError:
            logger.warning(f"✗ Timeout connecting to alert-notification server at {self.alert_server_url}")
            return False
        except Exception as e:
            logger.warning(f"✗ Alert server health check failed: {e}")
            return False
    
    async def check_all_servers_health(self, timeout: int = 5) -> tuple[bool, bool]:
        """Check health of both servers simultaneously"""
        shortage_health, alert_health = await asyncio.gather(
            self.check_shortage_server_health(timeout),
            self.check_alert_server_health(timeout),
            return_exceptions=True
        )
        
        # Handle exceptions
        if isinstance(shortage_health, Exception):
            shortage_health = False
        if isinstance(alert_health, Exception):
            alert_health = False
            
        return shortage_health, alert_health
    
    def get_shortage_server_startup_command(self) -> str:
        """Get the command to start the shortage-index server"""
        if self.shortage_server_path.exists():
            return f"cd {self.shortage_server_path.parent} && python server.py --host 0.0.0.0 --port 6970"
        else:
            return "python /merge/agent_develop/index/server.py --host 0.0.0.0 --port 6970"
    
    def get_alert_server_startup_command(self) -> str:
        """Get the command to start the alert-notification server"""
        if self.alert_server_path.exists():
            return f"cd {self.alert_server_path.parent} && python server.py --host 0.0.0.0 --port 6969"
        else:
            return "python /merge/agent_develop/notification/server.py --host 0.0.0.0 --port 6969"
    
    def display_startup_instructions(self, shortage_needed: bool = True, alert_needed: bool = True):
        """Display instructions for starting the required servers manually"""
        logger.info("=" * 70)
        logger.info("MCP SERVERS STARTUP REQUIRED")
        logger.info("=" * 70)
        logger.info("")
        
        if shortage_needed:
            logger.info("SHORTAGE-INDEX SERVER (Port 6970):")
            logger.info(f"Command: {self.get_shortage_server_startup_command()}")
            logger.info("Alternative: cd /merge/agent_develop && python -m index.server")
            logger.info("Expected: Server running on http://localhost:6970")
            logger.info("")
        
        if alert_needed:
            logger.info("ALERT-NOTIFICATION SERVER (Port 6969):")
            logger.info(f"Command: {self.get_alert_server_startup_command()}")
            logger.info("Alternative: cd /merge/agent_develop && python -m notification.server")
            logger.info("Expected: Server running on http://localhost:6969")
            logger.info("")
        
        logger.info("Please start the required servers in separate terminals.")
        logger.info("=" * 70)
    
    async def wait_for_servers(self, max_wait_time: int = 30, check_interval: int = 2) -> tuple[bool, bool]:
        """Wait for both servers to become available"""
        logger.info(f"Waiting up to {max_wait_time} seconds for servers to start...")
        
        for attempt in range(max_wait_time // check_interval):
            shortage_health, alert_health = await self.check_all_servers_health()
            
            if shortage_health and alert_health:
                logger.info("✓ Both servers are now available!")
                return True, True
            
            missing_servers = []
            if not shortage_health:
                missing_servers.append("shortage-index (6970)")
            if not alert_health:
                missing_servers.append("alert-notification (6969)")
            
            logger.info(f"Attempt {attempt + 1}/{max_wait_time // check_interval}: Waiting for {', '.join(missing_servers)}, waiting {check_interval}s...")
            await asyncio.sleep(check_interval)
        
        # Final check to return individual server status
        return await self.check_all_servers_health()


async def main():
    logger.info("=== Starting Financial Analyzer Integrated Workflow with SSE Transport ===")
    
    # Initialize dual server manager
    server_manager = DualServerManager()
    
    # Check if both servers are running
    logger.info("Checking server availability...")
    shortage_available, alert_available = await server_manager.check_all_servers_health()
    
    if not shortage_available or not alert_available:
        server_manager.display_startup_instructions(
            shortage_needed=not shortage_available,
            alert_needed=not alert_available
        )
        
        # Ask user if they want to wait for server startup
        logger.info("\nDo you want to wait for the servers to start? (recommended)")
        logger.info("Press Ctrl+C to exit, or the test will wait for 30 seconds...")
        
        try:
            shortage_available, alert_available = await server_manager.wait_for_servers(max_wait_time=30)
        except KeyboardInterrupt:
            logger.info("\nTest cancelled by user.")
            return False
        
        if not shortage_available or not alert_available:
            missing_servers = []
            if not shortage_available:
                missing_servers.append("shortage-index (6970)")
            if not alert_available:
                missing_servers.append("alert-notification (6969)")
            
            logger.error(f"Required servers not available: {', '.join(missing_servers)}")
            logger.info("Please start the servers manually and try again.")
            return False
    
    # Test scenarios from question.md
    basic_test_data = "cpu available is 1, require is 2, and gpu available is 2, require is 6, motherboard available is 1, require is 3, fans available is 1, require is 6"
    
    weighted_test_data = "cpu available is 1, require is 2, weight 0.2, and gpu available is 2, require is 6, weight 0.6, motherboard available is 1, require is 3, weight 0.1, fans available is 1, require is 6, weight 0.1"

    logger.info("Initializing MCP App with SSE transport...")
    async with app.run() as analyzer_app:
        context = analyzer_app.context
        logger.info("✓ MCP App initialized successfully")

        # Verify server configurations
        shortage_configured = "shortage-index" in context.config.mcp.servers
        alert_configured = "alert-notification" in context.config.mcp.servers
        logger.info(f"✓ Shortage server configured: {shortage_configured}")
        logger.info(f"✓ Alert server configured: {alert_configured}")
        
        if not shortage_configured or not alert_configured:
            logger.error("Required servers not configured in mcp_agent.config.yaml")
            logger.info("Please check the configuration file.")
            return False

        # Verify SSE transport configurations
        if shortage_configured:
            shortage_config = context.config.mcp.servers["shortage-index"]
            transport = getattr(shortage_config, "transport", None)
            url = getattr(shortage_config, "url", None)
            logger.info(f"✓ Shortage server - Transport: {transport}, URL: {url}")
            
        if alert_configured:
            alert_config = context.config.mcp.servers["alert-notification"]
            transport = getattr(alert_config, "transport", None)
            url = getattr(alert_config, "url", None)
            logger.info(f"✓ Alert server - Transport: {transport}, URL: {url}")
            
            if transport != "sse":
                logger.warning(f"Expected SSE transport for alert server, got: {transport}")

        # Create shortage analyzer agent
        shortage_analyzer = create_shortage_analyzer_agent("TestCompany")
        logger.info("✓ Shortage analyzer agent initialized with MCP SSE transport")
        
        # Create alert manager agent
        alert_manager = create_alert_manager_agent("TestCompany")
        logger.info("✓ Alert manager agent initialized with MCP SSE transport")
        
        # Initialize the agents' LLMs for MCP operations
        logger.info("Initializing agent LLMs for MCP SSE transport...")
        if hasattr(shortage_analyzer, 'initialize_llm'):
            await shortage_analyzer.initialize_llm()
            logger.info("✓ Shortage analyzer LLM initialized successfully")
        else:
            logger.warning("Shortage analyzer does not support LLM initialization - MCP calls may fail")
            
        if hasattr(alert_manager, 'initialize_llm'):
            await alert_manager.initialize_llm()
            logger.info("✓ Alert manager LLM initialized successfully")
        else:
            logger.warning("Alert manager does not support LLM initialization - MCP calls may fail")

        # Test Scenario 1: Basic Shortage Index via SSE
        logger.info("\n" + "=" * 60)
        logger.info("TEST SCENARIO 1: Basic Shortage Index via MCP SSE")
        logger.info("=" * 60)
        logger.info(f"Input: {basic_test_data}")
        logger.info("Expected: ShortageIndex tool call via SSE transport")
        
        try:
            # Create proper input schema with financial_data field
            basic_input = {
                "company_name": "TestCompany", 
                "financial_data": basic_test_data,
                "message": "Analyze shortage index using MCP SSE transport"
            }
            
            logger.info("Calling shortage analyzer with MCP SSE transport...")
            basic_result = await shortage_analyzer.enhanced_shortage_analysis(basic_input)
            
            logger.info("✓ Basic shortage analysis completed")
            logger.info(f"Result:\n{basic_result.response}")
            logger.info(f"Shortage Index: {basic_result.shortage_index}")
            logger.info(f"Risk Level: {basic_result.risk_level}")
            
            # Phase 2: Alert Processing and Notification Delivery
            logger.info("\n" + "=" * 60)
            logger.info("PHASE 2: ALERT PROCESSING & NOTIFICATION DELIVERY")
            logger.info("=" * 60)
            logger.info("Converting shortage analysis results to alert management input...")
            
            # Create AlertManagementInputSchema from shortage results
            alert_input = AlertManagementInputSchema(
                company_name=basic_result.company_name,
                analysis_data=basic_result.response,
                shortage_data=f"shortage_index is {basic_result.shortage_index:.3f}, risk_level is {basic_result.risk_level}, cpu shortage detected",
                alert_message="cpu matrial shortage alert - advanced planning and scheduling (APS) system has detected a shortage of CPU materials. Please take necessary actions to address this issue.",
                message="Process shortage analysis results and send notifications via HTTP, MQTT"
            )
            
            logger.info("Calling alert manager for notification processing...")
            try:
                alert_result = await alert_manager.process_financial_analysis(alert_input)
                
                logger.info("✓ Alert processing completed")
                logger.info(f"Alerts Generated: {len(alert_result.alerts_sent)}")
                logger.info(f"Alerts Sent: {alert_result.alerts_sent}")
                logger.info(f"Notification Results: {alert_result.notification_results}")
                logger.info(f"Alert Summary:\n{alert_result.alert_summary}")
                
            except Exception as alert_error:
                logger.error(f"✗ Error in alert processing: {str(alert_error)}")
                import traceback
                logger.debug(traceback.format_exc())
            
        except Exception as e:
            logger.error(f"✗ Error in basic shortage analysis: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())

        # Test Scenario 2: Weighted Shortage Index via SSE
        logger.info("\n" + "=" * 60)
        logger.info("TEST SCENARIO 2: Weighted Shortage Index via MCP SSE")
        logger.info("=" * 60)
        logger.info(f"Input: {weighted_test_data}")
        logger.info("Expected: WeightedShortageIndex tool call via SSE transport")
        
        try:
            # Create proper input schema with financial_data field
            weighted_input = {
                "company_name": "TestCompany",
                "financial_data": weighted_test_data,
                "message": "Analyze weighted shortage index using MCP SSE transport"
            }
            
            logger.info("Calling shortage analyzer with weighted analysis via MCP SSE...")
            weighted_result = await shortage_analyzer.enhanced_shortage_analysis(weighted_input)
            
            logger.info("✓ Weighted shortage analysis completed")
            logger.info(f"Result:\n{weighted_result.response}")
            logger.info(f"Shortage Index: {weighted_result.shortage_index}")
            logger.info(f"Risk Level: {weighted_result.risk_level}")
            
            # Phase 2: Alert Processing for Weighted Scenario
            logger.info("\n" + "=" * 60)
            logger.info("PHASE 2: WEIGHTED SCENARIO ALERT PROCESSING")
            logger.info("=" * 60)
            logger.info("Converting weighted shortage analysis results to alert management input...")
            
            # Create AlertManagementInputSchema from weighted shortage results
            weighted_alert_input = AlertManagementInputSchema(
                company_name=weighted_result.company_name,
                analysis_data=weighted_result.response,
                shortage_data=f"weighted_shortage_index is {weighted_result.shortage_index:.3f}, risk_level is {weighted_result.risk_level}, cpu and gpu shortage detected",
                alert_message="weighted cpu matrial shortage alert - advanced planning and scheduling (APS) system has detected weighted component shortages. Priority action required for critical components.",
                message="Process weighted shortage analysis results and send prioritized notifications via HTTP, MQTT"
            )
            
            logger.info("Calling alert manager for weighted scenario notification processing...")
            try:
                weighted_alert_result = await alert_manager.process_financial_analysis(weighted_alert_input)
                
                logger.info("✓ Weighted alert processing completed")
                logger.info(f"Alerts Generated: {len(weighted_alert_result.alerts_sent)}")
                logger.info(f"Alerts Sent: {weighted_alert_result.alerts_sent}")
                logger.info(f"Notification Results: {weighted_alert_result.notification_results}")
                logger.info(f"Alert Summary:\n{weighted_alert_result.alert_summary}")
                
            except Exception as weighted_alert_error:
                logger.error(f"✗ Error in weighted alert processing: {str(weighted_alert_error)}")
                import traceback
                logger.debug(traceback.format_exc())
            
        except Exception as e:
            logger.error(f"✗ Error in weighted shortage analysis: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())

        # Final server health check
        logger.info("\nPerforming final server health check...")
        final_shortage_health, final_alert_health = await server_manager.check_all_servers_health()
        
        logger.info("\n" + "=" * 70)
        logger.info("FINANCIAL ANALYZER INTEGRATED WORKFLOW COMPLETE")
        logger.info("=" * 70)
        logger.info(f"✓ Shortage Server Status: {'Healthy' if final_shortage_health else 'Unavailable'}")
        logger.info(f"✓ Alert Server Status: {'Healthy' if final_alert_health else 'Unavailable'}")
        logger.info("✓ MCP SSE Transport: Tested for both servers")
        logger.info("✓ ShortageIndex Tool: Tested")
        logger.info("✓ WeightedShortageIndex Tool: Tested")
        logger.info("✓ HttpNotification Tool: Tested via Alert Manager")
        logger.info("✓ MqttNotification Tool: Tested via Alert Manager")
        logger.info("✓ End-to-End Workflow: Shortage Analysis → Alert Processing → Notifications")
        logger.info("=" * 70)
        
        return True


if __name__ == "__main__":
    asyncio.run(main())
